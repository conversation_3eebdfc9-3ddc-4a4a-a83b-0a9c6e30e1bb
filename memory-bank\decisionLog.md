# Decision Log

This file records architectural and implementation decisions using a list format.
2025-08-04 12:10:06 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

---
### Decision
[2025-08-04 12:11:02] - 批准并制定了 `dimension_calculator.py` 的优化实施计划。

**Rationale:**
当前的维度计算方法存在信息丢失、维度冗余和计算不精确的问题。通过引入动态归一化、新的语言学特征和信息熵维度，旨在提升特征的表达能力，从而可能提高下游模型的性能。详细计划已存档于 `IMPROVEMENT_PLAN.md`。

**Implications/Details:**
- 将创建一个新的 `optimized_dimension_calculator.py` 文件进行开发，以确保安全过渡和可对比性。
- 核心变更包括移除两个冗余维度，深化一个现有维度，并引入一个新维度。
- `calculate_all_dimensions` 函数需要被重构以集成这些变更。

---
### Decision (Code)
[2025-08-04 12:27:24] - 确认继续按计划实施深化的 `boundary_ambiguity` 维度计算。

**Rationale:**
在实施过程中，用户对 `boundary_ambiguity` 维度的必要性提出了疑问。经过解释，该维度通过分析实体边界词的语言学特征（如词性、依存关系）来更精确地评估标注难度，对理解模型行为和指导训练具有潜在价值。

**Implications/Details:**
- 与用户达成一致，决定先完整实施该功能，后续通过实验结果来评估其最终效果和价值。
- 该功能的代码已按计划合并到 `dimension_calculator.py` 中。
## 2025-08-04: 集成优化后的维度计算器并实现动态权重

**决策**:
1.  **集成方式**: 直接在 `example_retriever.py` 中修改，而不是创建新的 `optimized_dimension_calculator.py` 文件。将 `dimension_calculator.py` 中的 `DimensionCalculator` 重命名为 `OptimizedDimensionCalculator` 进行导入，以明确表示使用的是优化版本。
2.  **动态权重**: 在 `ExampleRetriever` 中新增 `_calculate_dynamic_weights` 私有方法。该方法根据LLM的 `numerical_features` 需求与候选样本集的平均特征之间的“差距”来动态调整各维度的权重。
3.  **更新检索逻辑**: 修改 `_fuse_rankings_and_score` 方法，使其调用 `_calculate_dynamic_weights` 来获取动态权重，并应用于RRF融合算法，取代了原有的静态权重配置。

**理由**:
-   直接修改可以简化代码库结构，避免文件冗余。
-   动态权重机制能够使检索系统更智能地响应LLM的实时需求，当LLM需要某种特定特征的样本时，系统能够自动提升该特征在排序中的重要性，从而提高检索结果的相关性。

---
### Decision (Debug)
[2025-08-04 20:48:30] - 统一使用DimensionCalculator类

**Rationale:**
optimized_dimension_calculator.py文件不存在，而dimension_calculator.py中的DimensionCalculator类已包含所有优化功能

**Details:**
修改了example_retriever.py和main.py的导入语句，确保系统统一使用DimensionCalculator类

---
### Decision (Code)
[2025-08-04 14:13:57] - 重构高并发任务调度逻辑以解决API错误

**Rationale:**
系统在处理大规模数据集时频繁出现 `401 Unauthorized` 和 `503 Service Unavailable` 错误。经审查，根本原因在于 `main.py` 的任务调度逻辑会一次性创建并提交数千个 `asyncio` 任务，瞬间产生的请求洪峰压垮了作为代理的 `GPT-Load` 服务。尽管 `model_interface.py` 中存在 `Semaphore`，但它只能限制正在执行的请求，无法阻止海量连接请求的创建。

**Details:**
- **解决方案**: 采用“带信号量控制的并发任务调度”模式。在 `main.py` 的三个核心处理阶段，我们不再分批处理，而是：
    1. 一次性为所有样本创建 `asyncio.Task`。
    2. 使用一个独立的 `asyncio.Semaphore` 来包装每个任务的执行体。
    3. `Semaphore` 的计数由新的配置项 `concurrency_limit` (位于 `config.py`) 控制，确保同时运行的任务数被精确限制在一个合理的水平（例如64）。
- **代码修改**:
    - `main.py`: 在阶段1、2、3中，重构了任务创建和执行的循环，引入了 `controlled_worker` 模式。
    - `config.py`: 将无效的 `max_concurrent_requests` 配置重命名为 `concurrency_limit`，并设置了更合理的默认值，使其意图更清晰。
- **结果**: 此修改在保持代码并行性的前提下，实现了请求流量的平滑发送（“慢启动”），从根本上解决了服务器过载问题。

---
### Decision (Code)
[2025-08-04 14:27:48] - 清理冗余的批处理配置并统一嵌入层配置

**Rationale:**
在解决了核心的并发问题后，用户敏锐地指出，原有的 `batch_size` 和 `batch_delay` 配置项在 `main.py` 中已变得冗余。进一步的审查发现，嵌入层的批处理大小在 `example_retriever.py` 中被硬编码，而在 `model_interface.py` 中使用了另一个独立的配置。这种不一致性会降低代码的可维护性。

**Details:**
- **代码清理**:
    - `main.py`: 彻底移除了所有对 `batch_size` 和 `batch_delay` 的定义和使用，因为并发控制已由 `concurrency_limit` 全权负责。
    - `config.py`: 删除了通用的 `batch_size` 和 `batch_delay`，并引入了一个语义明确的 `embedding_batch_size`，专门用于控制嵌入API的批处理大小。
    - `model_interface.py`: 更新了 `get_embeddings_async` 方法，使其从配置中读取 `embedding_batch_size`。
    - `example_retriever.py`: 移除了 `_generate_embeddings_in_batches` 方法中的硬编码 `batch_size`，并重构了该函数，使其直接调用 `model_service` 来处理嵌入生成，从而统一了配置来源。
- **结果**: 本次重构消除了冗余配置，统一了嵌入层的批处理逻辑，显著提升了代码的清晰度和可维护性。
---
### Decision (Architecture)
[2025-08-05 05:34:00] - 实施系统级性能与稳定性优化

**Rationale:**
为了从根本上解决服务过载问题、提升数据检索性能和保证数据一致性，对系统的核心组件进行了多项关键升级。

**Details:**
- **智能任务调度器**: 在 `main.py` 中引入 `run_concurrent_tasks` 函数，通过 `requests_per_second` 和 `max_active_tasks` 配置，实现了对API请求的精准流量控制，解决了服务过载问题。
- **FAISS 高速引擎**: 在 `example_retriever.py` 中集成了 FAISS，实现了工业级的向量检索，大幅提升了性能。
- **健壮的缓存机制**: 缓存从 JSON 升级为带版本控制和哈希验证的 `.pkl` 格式，并支持自动迁移，保证了数据一致性。
- **统一的嵌入层**: 嵌入向量的生成逻辑已统一到 `model_interface.py`，并由 `config.py` 中的 `embedding_batch_size` 统一配置，提高了可维护性。

---
### Decision (Architecture)
[2025-08-05 05:34:05] - 采纳多维自适应检索 (Multi-dimensional Adaptive Retrieval) 算法

**Rationale:**
为了超越传统的基于单一语义相似度的检索，引入一种能够根据实时需求动态调整策略的、更智能和多维度的检索范式。

**Details:**
- **特征预计算**: 系统现在使用 `dimension_calculator.py` 为每个训练样本预计算丰富的“维度特征”（如实体密度、句法复杂度），并存入 `metadata`。
- **动态权重 (核心创新)**: `example_retriever.py` 中的 `_calculate_dynamic_weights` 方法能够根据“LLM的需求”与“候选样本池的平均特征”之间的实时差距，动态调整各维度的权重。
- **RRF 融合**: 使用“倒数排名融合 (RRF)”算法，智能地结合语义相似度排名与所有维度特征的排名，生成最终的综合排序，取代了原有的静态加权方法。

---
### Decision (Code)
[2025-08-05 06:10:22] - Refactored `run_concurrent_tasks` to handle stage timeouts gracefully.

**Rationale:**
The previous implementation used a `break` statement upon timeout, which could lead to the abrupt cancellation of in-flight tasks, causing `499 context canceled` errors. The new logic ensures that once a timeout is triggered, no new tasks are dispatched, but all active tasks are allowed to complete naturally.

**Details:**
- Modified `main.py` in the `run_concurrent_tasks` function.
- Introduced a `timeout_triggered` boolean flag.
- When timeout occurs, the flag is set to `true`, and the `task_queue` is cleared to prevent new tasks from starting.
- The main `while` loop continues until `active_tasks` is empty, ensuring all dispatched tasks are completed.