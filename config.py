import os

CONFIG = {
    # ====== 🚀 核心配置 ======
    'max_test_samples': 1500,
    'embedding_model_path': 'BAAI/bge-m3',

    # ====== 🔗 API配置 ======
    'base_url': 'https://scqtntfzyyqs.ap-southeast-1.clawcloudrun.com/proxy/silicon/v1',
    'model_name': 'Qwen/Qwen3-8B',
    'api_key': 'sk-zhongyushi',


    # ====== 🚀 并发控制配置 ======
    'concurrency_control': {
        'requests_per_second': 10,      # 每秒发送的请求数
        'max_active_tasks': 50,         # 同时活跃的最大任务数
        'embedding_batch_size': 64,     # 嵌入API的批处理大小，避免超出模型限制
    },

    # ====== ⏱️ 超时配置 ======
    'timeouts': {
        'api_request': 120,      # 单个API请求超时 (降低到2分钟，避免499错误)
        'stage2_batch': 900,     # 阶段2批处理超时(15分钟)
        'stage3_batch': 1800,    # 阶段3批处理超时(30分钟)
        'single_task': 300,      # 单个任务超时(5分钟)
    },

    # ====== 🔄 重试配置 ======
    'retry': {
        'max_attempts': 3,       # 最大重试次数
        'delay_seconds': 5,      # 重试延迟(秒)
    },

    # ====== 📊 界面配置 ======
    'log_level': 'info',

    # ====== 📁 路径配置 ======
    'cache_dir': './cache',
    'data_root_dir': './data',

    # ====== 🔍 检索配置 ======
    'retrieval_config': {
        'vector_top_k': 30,
        'reranker_top_k': 10,
        'final_examples_count': 3,
        'score_threshold': 0.1,
        'diversity_lambda': 0.3,
    },



    # ====== 🧠 需求描述检索配置 ======
    'needs_based_retrieval_config': {
        'enabled': True,   # 启用基于需求描述的检索功能
        'use_metacognitive_prompt': True,  # 使用元认知prompt
        'fallback_to_traditional': True,   # 失败时回退到传统方法
        'candidate_multiplier': 6,         # 候选数量倍数
        'min_candidates': 30,              # 最小候选数量
        'score_fusion_weights': {
            'original_score': 0.3,         # 原始检索分数权重
            'rrf_score': 0.7               # RRF融合分数权重
        },
        'dimension_weights': {
            'semantic_need': 1.0,          # 语义需求权重
            'syntactic_need': 0.8,         # 句法需求权重
            'entity_need': 0.9,            # 实体需求权重
            'style_need': 0.7              # 风格需求权重
        }
    },

    # ====== 📊 数据集配置 ======
    'dataset': 'conll2003',
    'current_dataset': 'conll2003',  # {{ AURA-X: Fix - 添加缺失的current_dataset配置. Approval: 寸止(ID:1738230405). }}
    'datasets': {
        'ace2005': {
            'name': 'ACE 2005',
            'path': 'data/ACE 2005/train.json',
            'labels': ['person', 'organization', 'location', 'facility', 'weapon', 'vehicle', 'geo-political'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "John Smith", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")"""
        },
        'conll2003': {
            'name': 'CoNLL 2003',
            'path': 'data/CoNLL2003/train.json',
            'labels': ['PER', 'ORG', 'LOC', 'MISC'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from CoNLL 2003 dataset:**
- PER: Person names (e.g., "John Smith", "Mary Johnson")
- ORG: Organizations (e.g., "Apple Inc.", "Microsoft")
- LOC: Locations (e.g., "New York", "California")
- MISC: Miscellaneous named entities"""
        }
    }
}


def validate_concurrency_config():
    """
    验证并发控制配置，确保参数合理
    """
    config = CONFIG.get('concurrency_control', {})

    print("🔍 验证并发控制配置...")

    # 验证基本参数
    requests_per_second = config.get('requests_per_second', 10)
    max_active_tasks = config.get('max_active_tasks', 50)
    embedding_batch_size = config.get('embedding_batch_size', 64)

    # 合理性检查
    if requests_per_second <= 0:
        print("⚠️ requests_per_second 应该大于0")
    elif requests_per_second > 100:
        print("💡 requests_per_second 过高可能导致API限制")

    if max_active_tasks <= 0:
        print("⚠️ max_active_tasks 应该大于0")
    elif max_active_tasks > 200:
        print("💡 max_active_tasks 过高可能消耗过多内存")

    if embedding_batch_size <= 0:
        print("⚠️ embedding_batch_size 应该大于0")
    elif embedding_batch_size > 100:
        print("💡 embedding_batch_size 过大可能导致API超时")

    # 关系检查
    if max_active_tasks < requests_per_second:
        print("💡 建议 max_active_tasks >= requests_per_second 以避免任务积压")

    print("✅ 并发控制配置验证完成")


def validate_needs_retrieval_config():
    """
    验证基于需求的检索配置，并提供优化建议
    """
    config = CONFIG.get('needs_based_retrieval_config', {})

    if not config.get('enabled', False):
        print("ℹ️ 基于需求的检索功能未启用")
        return

    print("🔍 验证基于需求的检索配置...")

    # 验证权重配置
    dimension_weights = config.get('dimension_weights', {})
    required_dimensions = ['semantic_need', 'syntactic_need', 'entity_need', 'style_need']

    missing_weights = [dim for dim in required_dimensions if dim not in dimension_weights]
    if missing_weights:
        print(f"⚠️ 缺少维度权重配置: {missing_weights}")

    # 验证权重合理性
    total_weight = sum(dimension_weights.values())
    if abs(total_weight - 3.4) > 0.5:  # 期望总权重约为3.4 (1.0+0.8+0.9+0.7)
        print(f"💡 建议调整维度权重，当前总权重: {total_weight:.2f}")

    # 验证融合权重
    score_fusion = config.get('score_fusion_weights', {})
    fusion_total = score_fusion.get('original_score', 0.3) + score_fusion.get('rrf_score', 0.7)
    if abs(fusion_total - 1.0) > 0.01:
        print(f"⚠️ 分数融合权重总和应为1.0，当前: {fusion_total:.2f}")

    # 性能建议
    candidate_multiplier = config.get('candidate_multiplier', 6)
    if candidate_multiplier < 4:
        print("💡 建议增加candidate_multiplier以提高检索质量")
    elif candidate_multiplier > 10:
        print("💡 candidate_multiplier过大可能影响性能")

    print("✅ 配置验证完成")


def get_optimized_rrf_config():
    """
    获取优化的RRF配置建议
    """
    return {
        'k_constant': 60,  # 标准RRF常数
        'enable_adaptive_k': True,  # 启用自适应k值
        'score_boost_factor': 0.1,  # 原始分数增强因子
        'weight_normalization': True,  # 权重归一化
        'quality_threshold': 0.1,  # 质量阈值
    }

# ====== 🛠️ 工具函数 ======
def set_dataset(dataset_key: str) -> bool:
    """切换数据集"""
    if dataset_key in CONFIG['datasets']:
        CONFIG['current_dataset'] = dataset_key
        return True
    return False

def get_current_dataset_info() -> dict:
    """获取当前数据集信息"""
    return CONFIG['datasets'][CONFIG['current_dataset']]

def get_current_dataset_path() -> str:
    """获取当前数据集路径"""
    return get_current_dataset_info()['path']

def get_current_label_prompt() -> str:
    """获取当前数据集的标签prompt"""
    return get_current_dataset_info()['label_prompt']

def list_available_datasets() -> dict:
    """列出所有可用数据集"""
    available = {}
    for key, info in CONFIG['datasets'].items():
        available[key] = {
            'name': info['name'],
            'available': os.path.exists(info['path']),
            'current': key == CONFIG['current_dataset'],
            'labels': info.get('labels', [])
        }
    return available





def get_dataset_cache_dir() -> str:
    """获取当前数据集的缓存目录"""
    dataset_name = get_current_dataset_info()['name'].lower().replace(' ', '_')
    cache_dir = CONFIG['cache_dir']
    dataset_cache_dir = f"{cache_dir}/{dataset_name}"
    os.makedirs(dataset_cache_dir, exist_ok=True)
    return dataset_cache_dir

def get_cache_path(cache_type: str) -> str:
    """获取缓存文件路径"""
    dataset_cache_dir = get_dataset_cache_dir()
    return f"{dataset_cache_dir}/{cache_type}.json"


def initialize_datasets():
    """初始化数据集配置"""
    print("🚀 初始化数据集配置...")
    validate_concurrency_config()
    print("✅ 数据集配置已加载")


# ====== 🧠 轻量级维度注册表 ======
# 把所有维度的配置都集中在这里，方便实验和调整

# 从你的计算器导入函数 (假设 dimension_calculator.py 在同一目录下)
try:
    from dimension_calculator import DimensionCalculator
    dim_calc = DimensionCalculator()

    DIMENSION_REGISTRY = {
        'entity_density': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_entity_density,
            'similarity_params': {'sigma': 1.0, 'weight': 0.2}
        },
        'boundary_ambiguity': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_boundary_ambiguity,
            'similarity_params': {'sigma': 0.3, 'weight': 0.2}
        },
        'dependency_depth': {
            'type': 'numerical',
            'calculator': lambda text, entities: float(dim_calc.calculate_dependency_depth(text)),
            'similarity_params': {'sigma': 2.0, 'weight': 0.2}
        },
        'formality_level': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_formality_level,
            'similarity_params': {'sigma': 0.2, 'weight': 0.1}
        },
        'rhetorical_role': {
            'type': 'categorical',
            'calculator': dim_calc.calculate_rhetorical_role,
            'similarity_params': {'weight': 0.1}
        }
    }
except ImportError as e:
    print(f"⚠️ 无法导入DimensionCalculator: {e}")
    DIMENSION_REGISTRY = {}



