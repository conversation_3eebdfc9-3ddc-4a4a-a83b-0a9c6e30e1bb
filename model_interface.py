import logging
import asyncio
from typing import List, Dict, Any, Optional, Type
from pydantic import BaseModel

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionMessageParam
from openai.types.chat.chat_completion_tool_param import ChatCompletionToolParam

from config import CONFIG

# 配置日志
logger = logging.getLogger(__name__)

def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> ChatCompletionToolParam:
    """将Pydantic模型转换为OpenAI Function Calling工具的JSON Schema。"""
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }

class ModelService:
    """
    统一的模型服务层 - 优化版
    - 移除了内部信号量，由外部调用者（main.py）统一控制并发。
    - 实现了嵌入批处理的并行化。
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ModelService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config = CONFIG
            self.base_url = self.config.get('base_url')
            self.model_name = self.config.get('model_name')
            self.api_key = self.config.get('api_key')
            self.timeout = self.config.get('timeouts', {}).get('api_request', 60)

            retry_config = self.config.get('retry', {})
            self.api_max_retries = retry_config.get('max_attempts', 3)
            self.api_retry_delay = retry_config.get('delay_seconds', 2)

            self._client_pool = {}
            self.initialized = True
            logger.info(f"ModelService (优化版) initialized: timeout={self.timeout}s")

    async def _get_client(self) -> AsyncOpenAI:
        client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
        if client_key not in self._client_pool:
            self._client_pool[client_key] = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout,
            )
        return self._client_pool[client_key]

    async def generate_with_tools_async(self, messages: List[ChatCompletionMessageParam], tools: List[Type[BaseModel]]):
        """异步调用LLM，并使用Function Calling。"""
        if not self.api_key:
            logger.error("No API key configured.")
            return None

        tool_schemas: List[ChatCompletionToolParam] = [pydantic_to_openai_tool(tool) for tool in tools]

        for attempt in range(self.api_max_retries):
            try:
                client = await self._get_client()
                response = await client.chat.completions.create(
                    model=str(self.model_name),
                    messages=messages,
                    tools=tool_schemas,
                    tool_choice="auto",
                )
                return response.choices.message
            except Exception as e:
                logger.warning(f"API call failed on attempt {attempt + 1}: {e}")
                if "context canceled" in str(e).lower():
                    logger.error(f"❌ Context canceled detected on attempt {attempt + 1}: {e}")
                if attempt < self.api_max_retries - 1:
                    await asyncio.sleep(self.api_retry_delay)
                else:
                    logger.error("API call failed after multiple retries.")
                    return None

    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """🔍 异步获取文本嵌入向量 - 支持并行化大批量处理"""
        if not texts or not self.api_key:
            return []

        model_to_use = self.config['embedding_model_path']
        max_batch_size = self.config.get('concurrency_control', {}).get('embedding_batch_size', 64)

        batches = [texts[i:i + max_batch_size] for i in range(0, len(texts), max_batch_size)]
        if len(batches) > 1:
            logger.info(f"🔄 并行嵌入批处理: {len(texts)}个文本分为{len(batches)}批")

        tasks = [self._get_single_batch_embeddings(batch, model_to_use) for batch in batches]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        all_embeddings: List[List[float]] = []
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                logger.error(f"❌ 嵌入批处理失败 (批次 {i}): {result}")
                all_embeddings.extend([[] for _ in range(len(batches[i]))])
            elif result:
                all_embeddings.extend(result)
        return all_embeddings

    async def _get_single_batch_embeddings(self, texts: List[str], model_to_use: str) -> List[List[float]]:
        """获取单批嵌入向量"""
        for attempt in range(self.api_max_retries):
            try:
                client = await self._get_client()
                response = await client.embeddings.create(model=model_to_use, input=texts)
                logger.debug(f"✅ Embeddings generated for {len(texts)} texts")
                return [item.embedding for item in response.data]
            except Exception as e:
                logger.warning(f"⚠️ Embedding call failed on attempt {attempt + 1}: {e}")
                if attempt < self.api_max_retries - 1:
                    await asyncio.sleep(self.api_retry_delay)
                else:
                    logger.error("❌ Embedding call failed after multiple retries.")
                    return []
        return []

    async def generate_simple_async(self, messages: List[ChatCompletionMessageParam], temperature: float = 0.1) -> str:
        """🧠 简单文本生成"""
        if not self.api_key:
            logger.error("❌ No API key configured for text generation")
            return ""

        for attempt in range(self.api_max_retries):
            try:
                client = await self._get_client()
                response = await client.chat.completions.create(
                    model=str(self.model_name),
                    messages=messages,
                    temperature=temperature,
                    max_tokens=1000,
                )
                content = response.choices.message.content or ""
                logger.debug(f"✅ Generated {len(content)} characters of text")
                return content
            except Exception as e:
                logger.warning(f"⚠️ Simple generation failed on attempt {attempt + 1}: {e}")
                if attempt < self.api_max_retries - 1:
                    await asyncio.sleep(self.api_retry_delay)
                else:
                    logger.error("❌ Simple generation failed after multiple retries.")
                    return ""
        return ""

# 全局单例
model_service = ModelService()
