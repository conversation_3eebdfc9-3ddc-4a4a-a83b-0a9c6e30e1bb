# Product Context

This file provides a high-level overview of the project and the expected product that will be created.
* [2025-08-04 14:16:25] - Log of updates made.

## Project Goal
The project, named "APIICL", is a meta-cognitive agent system designed for Named Entity Recognition (NER). Its primary goal is to process datasets through a sophisticated three-stage pipeline to achieve high-accuracy NER results.

## Key Features
*   **Three-Stage Processing**: The system operates in three distinct, sequential stages:
    1.  **Retrieval Request Generation**: Analyzes each text sample to determine the specific type of examples needed for context.
    2.  **Example Retrieval**: Retrieves relevant examples based on the needs identified in stage 1.
    3.  **NER Execution**: Performs the final NER task using the original text augmented with the retrieved examples.
*   **High Concurrency**: Designed to process large datasets efficiently by making a high volume of concurrent API calls.
*   **Resilience and Stability**: Implements robust error handling, retries, and concurrency control to ensure stable operation under heavy load.
*   **Caching**: Caches intermediate results (retrieval requests, retrieved examples, NER results) to speed up subsequent runs.

## Overall Architecture
*   **Core Logic (`main.py`)**: Orchestrates the three-stage pipeline, manages task scheduling, and handles data flow.
*   **Model Interface (`model_interface.py`)**: A centralized service that handles all interactions with the underlying AI models (for generation, embeddings, etc.). It encapsulates API client logic, including authentication, retries, and concurrency limiting.
*   **Meta-Cognitive Agent (`meta_cognitive_agent.py`)**: The "brain" of the system, responsible for building prompts and executing the core logic for each of the three stages.
*   **Configuration (`config.py`)**: A centralized configuration file for all system parameters, including API endpoints, model names, and operational settings like timeouts and concurrency limits.
*   **Proxy Integration (`GPT-Load`)**: The system is designed to work with an external API proxy service (`GPT-Load`), which manages API key rotation and load balancing. The application code communicates with the proxy, not directly with the upstream AI service providers.