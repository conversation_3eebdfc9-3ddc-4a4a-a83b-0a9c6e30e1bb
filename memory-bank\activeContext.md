# Active Context

This file tracks the project's current status, including recent changes, current goals, and open questions.
* [2025-08-04 14:15:01] - Log of updates made.

## Current Focus
- The primary focus is now on ensuring the overall stability and performance of the system after the major concurrency logic refactoring.
- The next logical step is to run the full pipeline against a dataset to validate the fix and monitor for any new potential bottlenecks.

## Recent Changes
* **Concurrency Control Refactoring**: The task scheduling logic in `main.py` has been completely overhauled. It now uses a `Semaphore`-based approach to precisely control the number of concurrent API requests, preventing server overload.
* **Configuration Cleanup**: The configuration in `config.py` was updated for clarity. The ambiguous `max_concurrent_requests` was replaced with a clear and functional `concurrency_limit`.

## Open Questions/Issues
* There are no immediate open questions. The effectiveness of the implemented fix needs to be validated through a full test run.

* [2025-08-05 06:14:15] - **Concurrency Logic Fix**: The core task execution logic in `run_concurrent_tasks` (`main.py`) has been refactored to gracefully handle stage-level timeouts. This prevents the abnormal cancellation of active tasks and resolves potential `499 context canceled` errors, ensuring all in-flight operations can complete successfully.