# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-08-04 12:10:16 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

*   
---
### gpt-load: Enterprise AI Interface Proxy

**Timestamp:** 2025-08-04 13:35:30

**Role:**
`gpt-load` acts as a transparent proxy service for enterprise-level AI interfaces. It sits between our client application and the upstream AI services (e.g., OpenAI, Anthropic).

**Architecture:**
The data flow is as follows: `Client Application` -> `gpt-load Proxy` -> `Upstream AI Service`.

**Interaction Pattern:**
- **Simplified Client:** The client-side code does not need to implement complex logic for API key rotation, load balancing, or upstream endpoint management.
- **Configuration:**
    - The `base_url` in our application's configuration (e.g., [`config.py`](config.py)) must point to the `gpt-load` service endpoint.
    - The `api_key` must be a specific group-level API key provided by the proxy service (e.g., `sk-zhongyushi` for the `silicon` group). `gpt-load` uses this key for authentication and routing, not the actual upstream AI service key.

**Example (`config.py`):**
```python
# config.py
BASE_URL = "http://gpt-load.example.com/v1"  # Points to the gpt-load proxy
API_KEY = "sk-zhongyushi"                   # Group API key for authentication with the proxy
```

**Benefits:**
- **Centralized Management:** API keys, load balancing, and model routing are managed centrally by the `gpt-load` service.
- **Enhanced Security:** Upstream AI service keys are not exposed on the client side.
- **Scalability & Flexibility:** Easily switch or add new upstream models without changing client-side code.

## Testing Patterns

*

---
### Controlled Concurrency Task Scheduler

**Timestamp:** 2025-08-04 14:15:21

**Role:**
This pattern is used to execute a large number of asynchronous tasks while strictly controlling the number of tasks that run concurrently, preventing resource overload (e.g., hitting API rate limits, overwhelming a server with connections).

**Architecture:**
The pattern consists of three main components:
1.  **Task List**: A comprehensive list of all `asyncio.Task` objects to be executed.
2.  **Concurrency Limiter**: An `asyncio.Semaphore` initialized with a specific concurrency limit (e.g., 64).
3.  **Executor**: An `asyncio.as_completed` loop that iterates through the tasks as they finish.

**Interaction Pattern:**
1.  All tasks are created at once using `asyncio.create_task` and stored in a list.
2.  Each task's core logic is wrapped within an `async with semaphore:` block.
3.  The `asyncio.as_completed` loop processes results as they become available. The `Semaphore` ensures that no more than `concurrency_limit` tasks can enter the core logic block at any given time, effectively throttling the execution rate and creating a "slow start" or "smooth sending" effect.

**Example (`main.py`):**
```python
# 1. Define the concurrency limit and initialize the semaphore
concurrency_limit = CONFIG.get('concurrency_limit', 64)
semaphore = asyncio.Semaphore(concurrency_limit)

# 2. Define a worker function that acquires the semaphore
async def controlled_worker(params):
    async with semaphore:
        # Core logic, e.g., an API call
        return await some_api_call(params)

# 3. Create all tasks at once
tasks = [
    asyncio.create_task(controlled_worker(p))
    for p in all_params
]

# 4. Execute and process results
for future in asyncio.as_completed(tasks):
    result = await future
    # process result...
```
---
### Architectural Pattern: Multi-dimensional Adaptive Retrieval
**Timestamp:** 2025-08-05 05:36:10

**Role:**
This pattern describes an advanced retrieval system that moves beyond single-metric similarity search. It dynamically adapts its ranking strategy based on the specific requirements of a query (e.g., from an LLM) and the characteristics of the available data.

**Architecture:**
1.  **Offline Feature Pre-computation**: Before retrieval, a rich set of "dimensional features" (e.g., entity density, syntactic complexity, verbosity) is calculated for each item in the candidate pool using a dedicated module (`dimension_calculator.py`). These features are stored as metadata.
2.  **Dynamic Weight Calculation**: At query time, the system analyzes the query's needs (e.g., an LLM requesting samples with high entity density). It compares this need against the average features of the candidate pool and calculates a set of "dynamic weights" that emphasize the desired dimensions. This is handled by a specific method (`_calculate_dynamic_weights` in `example_retriever.py`).
3.  **Hybrid Ranking Fusion**: The system performs multiple rankings: one based on traditional semantic similarity (e.g., vector distance) and one for each of the pre-computed dimensions. These separate rank lists are then intelligently combined using a Reciprocal Rank Fusion (RRF) algorithm, weighted by the dynamic weights calculated in the previous step.

**Interaction Pattern:**
- A query is received, containing both a semantic component and a set of desired dimensional features.
- The system retrieves an initial set of candidates using a high-speed vector search (e.g., FAISS).
- It calculates dynamic weights based on the gap between the query's needs and the candidates' average features.
- It re-ranks the candidates using the RRF fusion of semantic and dimensional ranks.
- The final, hybrid-ranked list is returned.

**Benefits:**
- **Context-Aware**: Retrieval is no longer one-size-fits-all; it adapts to the specific, nuanced needs of each query.
- **Intelligent Ranking**: Avoids over-reliance on semantic similarity, allowing for the retrieval of more diverse and relevant results based on multiple criteria.
- **Explainable**: The dimensional weights provide insight into why certain items were ranked higher than others.

---
### Architectural Pattern: System-Level Performance & Stability Suite
**Timestamp:** 2025-08-05 05:36:15

**Role:**
A collection of integrated patterns designed to ensure high performance, stability, and maintainability for a data-intensive application that interacts heavily with external APIs.

**Components & Patterns:**

1.  **Intelligent API Throttling & Scheduling:**
    *   **Pattern**: Instead of relying on simple concurrency limits, this pattern uses a sophisticated scheduler (`run_concurrent_tasks` in `main.py`) that manages both the rate of new requests (`requests_per_second`) and the total number of in-flight operations (`max_active_tasks`).
    *   **Benefit**: Prevents service overloads and API rate-limiting errors by smoothing out request bursts, ensuring a steady and predictable load on external services.

2.  **High-Speed Vector Search Integration:**
    *   **Pattern**: Offloads nearest-neighbor vector search from pure Python/NumPy to a highly optimized, industry-standard library like FAISS (`example_retriever.py`).
    *   **Benefit**: Drastically reduces retrieval latency, enabling real-time performance on large-scale vector datasets.

3.  **Robust, Versioned Caching:**
    *   **Pattern**: Caching is implemented using a binary format (`.pkl`) that includes metadata for versioning and content validation (e.g., a hash of the source data). The system includes logic for automatic cache invalidation and migration if the source data or format changes.
    *   **Benefit**: Guarantees data consistency, prevents the use of stale or corrupt caches, and simplifies development by automating cache management.

4.  **Unified & Configurable Service Layers:**
    *   **Pattern**: Core functionalities, like generating embeddings, are centralized into a single service module (`model_interface.py`). Critical parameters, such as the batch size for embedding generation (`embedding_batch_size`), are externalized to a central configuration file (`config.py`) rather than being hardcoded in multiple places.
    *   **Benefit**: Improves code maintainability, reduces redundancy, and allows for easy tuning of system parameters without code changes.