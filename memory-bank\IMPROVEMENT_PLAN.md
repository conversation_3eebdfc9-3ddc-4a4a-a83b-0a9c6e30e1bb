# 维度计算器优化实施计划

## 1. 目标

本次优化的核心目标是解决 `dimension_calculator.py` 中存在的几个关键问题：
- **信息丢失**: 当前的维度计算方法（如硬性截断）会导致原始数据信息的损失。
- **维度冗余**: `information_density` 和 `instruction_complexity` 等维度存在概念重叠和冗余。
- **计算不精确**: `boundary_ambiguity` 等维度的计算逻辑过于简单，无法准确捕捉复杂的语言学现象。

通过引入更科学的计算方法和新的维度，我们旨在提升特征的表达能力和模型的最终性能。

## 2. 待办事项 (TODO List)

- [ ] **移除冗余维度**:
    - 从 `calculate_all_dimensions` 函数中移除对 `information_density` 的调用。
    - 从 `calculate_all_dimensions` 函数中移除对 `instruction_complexity` 的调用。
- [ ] **引入动态归一化**:
    - 在 `calculate_entity_density` 中应用对数归一化。
    - 在 `calculate_dependency_depth` 中应用 Min-Max 缩放。
- [ ] **深化 `boundary_ambiguity` 计算**:
    - 修改 `calculate_boundary_ambiguity` 函数，利用 spaCy 提供的词性（Part-of-Speech, POS）和依存关系（Dependency, DEP）信息来更精确地评估边界模糊度。
- [ ] **引入新维度 `entity_type_distribution`**:
    - 创建新的函数 `calculate_entity_type_distribution`。
    - 使用信息熵（Shannon Entropy）来计算实体类型的分布均衡度。
- [ ] **更新主计算函数**:
    - 修改 `calculate_all_dimensions` 函数，集成上述所有变更，调用新的和修改后的函数。
- [ ] **创建安全过渡文件**:
    - 复制 `dimension_calculator.py` 到 `optimized_dimension_calculator.py`。
    - 在新文件中实施所有变更，以便于对比测试和安全回滚。

## 3. 伪代码实现

### 3.1. `calculate_entity_type_distribution`

```pseudocode
FUNCTION calculate_entity_type_distribution(entities):
    """
    计算实体类型分布的信息熵。
    熵越高，表示实体类型分布越均匀、越复杂。
    """
    IF entities is empty:
        RETURN 0.0

    // 1. 统计每种实体类型的频率
    type_counts = new Counter()
    FOR entity IN entities:
        type_counts.increment(entity.type)

    // 2. 计算概率分布
    total_entities = length(entities)
    probabilities = []
    FOR count IN type_counts.values():
        probabilities.append(count / total_entities)

    // 3. 计算信息熵
    entropy = 0.0
    FOR p IN probabilities:
        IF p > 0:
            entropy -= p * log2(p)

    RETURN entropy
```

### 3.2. `calculate_boundary_ambiguity` (修改后)

```pseudocode
FUNCTION calculate_boundary_ambiguity(doc, entities):
    """
    利用语言学特征（词性、依存关系）重新计算边界模糊度。
    """
    IF entities is empty:
        RETURN 0.0

    ambiguity_score = 0.0
    boundary_tokens = new Set()

    // 1. 收集所有实体的边界词
    FOR entity IN entities:
        boundary_tokens.add(entity.start_token)
        boundary_tokens.add(entity.end_token)

    // 2. 分析每个边界词的语言学特征
    FOR token IN boundary_tokens:
        // 规则1: 如果边界词是标点符号，增加模糊度
        IF token.pos_ == 'PUNCT':
            ambiguity_score += 0.2

        // 规则2: 如果边界词是并列连词 (e.g., 'and', 'or')，显著增加模糊度
        IF token.dep_ == 'cc':
            ambiguity_score += 0.5

        // 规则3: 如果边界词是形容词或副词修饰名词，增加模糊度
        // (检查其父节点是否为名词，且关系为修饰关系)
        IF token.pos_ IN ['ADJ', 'ADV'] AND token.head.pos_ == 'NOUN':
            ambiguity_score += 0.3

    // 3. 归一化分数 (除以边界词数量，避免长文本偏见)
    IF length(boundary_tokens) > 0:
        normalized_score = ambiguity_score / length(boundary_tokens)
    ELSE:
        normalized_score = 0.0

    RETURN normalized_score
```

### 3.3. 应用归一化

#### `calculate_entity_density` (对数归一化)

```pseudocode
FUNCTION calculate_entity_density(doc, entities):
    """
    使用对数归一化计算实体密度，缓解极端值影响。
    """
    num_tokens = length(doc)
    num_entities = length(entities)

    IF num_tokens == 0:
        RETURN 0.0

    raw_density = num_entities / num_tokens
    
    // 应用对数归一化 (加1避免log(0))
    normalized_density = log(1 + raw_density)

    RETURN normalized_density
```

#### `calculate_dependency_depth` (Min-Max 缩放)

```pseudocode
FUNCTION calculate_dependency_depth(doc):
    """
    使用Min-Max缩放归一化平均依存深度。
    """
    // ... (计算每个词的深度并得到平均值 avg_depth) ...

    // 假设我们预设了理论上的最大深度和最小深度
    // 这些值可以从训练数据中统计得出
    MIN_DEPTH = 1.0
    MAX_DEPTH = 15.0 // 示例值

    // 应用Min-Max缩放
    scaled_depth = (avg_depth - MIN_DEPTH) / (MAX_DEPTH - MIN_DEPTH)

    // 确保结果在 [0, 1] 范围内
    RETURN max(0, min(1, scaled_depth))
```

## 4. 文件变更

- **需修改文件**:
    - `dimension_calculator.py`: 将被重构以实现上述优化。
- **建议创建新文件**:
    - `optimized_dimension_calculator.py`: 建议在此文件中进行所有修改。这允许我们保留原始实现作为基线，方便进行性能对比和A/B测试，确保平稳过渡。